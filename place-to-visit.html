﻿<!DOCTYPE html>
 <html lang="en">
<head>
<meta charset="utf-8">
<title>ISAR 2026 | Places to Visit</title>

<!-- Preload critical CSS -->
<link rel="preload" href="css/footer-custom.css" as="style">
<link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" as="style">

<!-- Base stylesheets first -->
<link href="css/bootstrap.css" rel="stylesheet">
<link href="css/style.css" rel="stylesheet">
<link href="css/responsive.css" rel="stylesheet">

<!-- Font Awesome -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

<!-- Custom CSS with high specificity - MUST load after main CSS -->
<link rel="stylesheet" href="css/footer-custom.css" type="text/css" media="all">
<link rel="stylesheet" href="css/places-to-visit.css" type="text/css" media="all">
<link rel="stylesheet" href="css/place-to-visit.css" type="text/css" media="all">
<link rel="stylesheet" href="css/swiper.css" type="text/css" media="all">
<link rel="stylesheet" href="css/intro-section.css" type="text/css" media="all">

<!-- Additional stylesheets -->
<link href="css/color-switcher-design.css" rel="stylesheet">

<link rel="shortcut icon" href="images/favicon.jpg" type="image/x-icon">
<link rel="icon" href="images/favicon.jpg" type="image/x-icon">

<!-- Responsive -->
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
<meta http-equiv="Pragma" content="no-cache">
<meta http-equiv="Expires" content="0">

<!--[if lt IE 9]><script src="https://cdnjs.cloudflare.com/ajax/libs/html5shiv/3.7.3/html5shiv.js"></script><![endif]-->
<!--[if lt IE 9]><script src="js/respond.js"></script><![endif]-->
</head>

<body>

<div class="page-wrapper">

    <!-- Preloader -->
    <div class="preloader"></div>
 	<!-- Header span -->

    <!-- Header Span -->
    <span class="header-span"></span>

    <!-- Main Header-->
    <header class="main-header header-style-two alternate">

		<div class="header-top">
			<div class="auto-container">
				<div class="clearfix">

					<div class="top-left pull-left">
						<div class="header-contact-info">
                            <div class="call"><a href="tel:+************"><img src="/images/icons/call.svg" alt="" loading="lazy" width="16" height="16">  +91 97272 42852</a></div>
                            <div class="email"><a href="mailto:<EMAIL>"> <img src="/images/icons/mail.svg" alt="" loading="lazy" width="16" height="16">  <EMAIL></a></li></div>
                        </div>
					</div>

					<div class="pull-right">
						<ul class="social-links">
							<li><a href="http://facebook.com/30thisar2026" target="_blank"><i class="fab fa-facebook-f"></i></a></li>
							<li><a href="https://youtube.com/@isar2026" target="_blank"><i class="fab fa-youtube"></i></a></li>
							<li><a href="https://www.instagram.com/isar_2026/" target="_blank"><i class="fab fa-instagram"></i></a></li>
						</ul>
						<a href="registration.html" class="theme-btn register-btn">Registration</a>
                         <div class="header-logo btn-logo"><img src="images/isar-combine-logo.png" alt="" loading="lazy" width="120" height="40"></div>
					</div>

				</div>
			</div>
		</div>

        <div class="main-box">
            <div class="auto-container clearfix">
                <div class="logo-box logo-header">
                    <div class="logo"><a href="index.html"><img src="images/isar-logo.png" alt="" title="" width="200" height="80"></a></div>
                </div>

                <!--Nav Box-->
                <div class="nav-outer clearfix">
                    <!--Mobile Navigation Toggler-->
                    <div class="mobile-nav-toggler"><span class="icon flaticon-menu"></span></div>
                    <!-- Main Menu -->
                    <nav class="main-menu navbar-expand-lg navbar-light">
                        <div class="navbar-header">
                            <!-- Togg le Button -->
                            <button class="navbar-toggler d-none" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                                <span class="icon flaticon-menu-button"></span>
                            </button>
                        </div>

                        <div class="collapse navbar-collapse clearfix" id="navbarSupportedContent">
                            <ul class="navigation clearfix">
                                <li><a href="index.html">Home</a>
                                <li class="dropdown"><a href="#">Committee</a>
                                    <ul>
                                        <li><a href="national-excutive-committee.html">National Executive Committee</a></li>
                                        <li><a href="local-org-committee.html">Local Organization Committee</a></li>
                                    </ul>
                                </li>
                                <li><a href="speakers.html">Speakers</a></li>
                                <li><a href="registration.html">Registration</a></li>
                                <li><a href="abstract.html">Abstract</a></li>
                                <li><a href="schedule.html">Schedule</a>
                                <li class="dropdown"><a href="#">Travel</a>
                                    <ul>
                                        <li><a href="place-to-visit.html">Place to Visit</a></li>
                                        <li><a href="accommodation.html">Accomodation</a></li>
                                        <li><a href="visa-guideline.html">Visa Guideline</a></li>
                                    </ul>
                                </li>   
                                <li><a href="contact.html">Contact Us</a></li>
                                </li>
                            </ul>
                        </div>
                    </nav>
                    
                    <!-- Main Menu End-->                   
                </div>
            </div>
        </div>

        <!-- Mobile Menu  -->
        <div class="mobile-menu">
            <div class="menu-backdrop"></div>
            <div class="close-btn"><span class="icon flaticon-cancel-1"></span></div>

            <!--Here Menu Will Come Automatically Via Javascript / Same Menu as in Header-->
            <nav class="menu-box">
                <div class="nav-logo"><a href="index.html"><img src="images/isar-logo.png" alt="" title="" loading="lazy" width="200" height="80"></a></div>

                <ul class="navigation clearfix"><!--Keep This Empty / Menu will come through Javascript-->
                </ul>
            </nav>
        </div><!-- End Mobile Menu -->
    </header>
    <!--End Main Header -->

    <!--Page Title-->
    <section class="page-title" style="background-image:url(images/background/ahmedabad.png);">
        <div class="auto-container">
            <h1>Places to Visit</h1>
            <ul class="bread-crumb clearfix">
                <li>Travel</li>
                <li>Places to Visit</li>
            </ul>
        </div>
    </section>
    <!--End Page Title-->

    <!-- Introduction Section with Parallax -->
    <section class="intro-section-container">
        <div class="intro-bg-element"></div>
        <div class="auto-container">
            <div class="intro-text-container">
                <h2 class="intro-heading">Discover Gujarat & Ahmedabad</h2>
                <p class="intro-paragraph">Explore the vibrant culture, rich heritage, and natural wonders of Gujarat and Ahmedabad. From ancient temples and historic sites to modern marvels and breathtaking landscapes, this region offers a diverse range of experiences for every traveler.</p>
            </div>
        </div>
    </section>

    <!-- 3D Slider Gallery Section -->
    <section class="slider-gallery-section">
        <div class="auto-container">
            <div class="section-title">
                <h2>Top Attractions</h2>
            </div>
            <div class="slider-container">
                <div class="swiper-container attractions-slider">
                    <div class="swiper-wrapper">
                        <!-- Slider items will be populated with JavaScript -->
                    </div>
                </div>
                <div class="swiper-button-next"></div>
                <div class="swiper-button-prev"></div>
            </div>
        </div>
    </section>

    <!-- Location Tabs and Filters Section -->
    <section class="places-filter-section">
        <div class="auto-container">
            <div class="location-tabs">
                <button class="location-tab active" data-location="gujarat">Gujarat</button>
                <button class="location-tab" data-location="ahmedabad">Ahmedabad</button>
            </div>
            
            <div class="category-filters" id="gujarat-filters">
                <button class="category-filter active" data-filter="all">All</button>
                <button class="category-filter" data-filter="historical">Historical & Architectural</button>
                <button class="category-filter" data-filter="religious">Religious Sites</button>
                <button class="category-filter" data-filter="wildlife">Wildlife & Nature</button>
                <button class="category-filter" data-filter="heritage">Heritage & Cultural</button>
                <button class="category-filter" data-filter="natural">Natural Wonders</button>
                <button class="category-filter" data-filter="modern">Modern Attractions</button>
            </div>
            
            <div class="category-filters" id="ahmedabad-filters" style="display: none;">
                <button class="category-filter active" data-filter="all">All</button>
                <button class="category-filter" data-filter="historical">Historical & Cultural</button>
                <button class="category-filter" data-filter="museums">Museums & Art</button>
                <button class="category-filter" data-filter="modern">Modern Attractions</button>
                <button class="category-filter" data-filter="architecture">Architectural Marvels</button>
            </div>
        </div>
    </section>

    <!-- Places Grid Section -->
    <section class="places-grid-section">
        <div class="auto-container">
            <div class="places-grid" id="gujarat-places">
                <!-- Gujarat places cards will be populated with JavaScript -->
            </div>
            
            <div class="places-grid" id="ahmedabad-places" style="display: none;">
                <!-- Ahmedabad places cards will be populated with JavaScript -->
            </div>
        </div>
    </section>

    <!-- Magazine Style Layout Section -->
    <section class="magazine-section" style="display: none;">
        <div class="auto-container">
            <div class="magazine-title-section">
                <h2>Explore Gujarat's Beauty</h2>
            </div>
            
            <div class="places-magazine-grid" id="gujarat-magazine">
                <!-- Feature item (large) -->
                <div class="magazine-item magazine-feature" data-category="historical" data-id="rani-ki-vav-patan">
                    <div class="magazine-img-container">
                        <img src="images/places/rani-ki-vav.jpg" alt="Rani Ki Vav">
                    </div> 
                    <div class="magazine-overlay">
                        <span class="magazine-category historical">Historical</span>
                        <h3 class="magazine-title">Rani Ki Vav (The Queen's Stepwell)</h3>
                        <p class="magazine-location">Patan, Gujarat</p>
                        <p class="magazine-description">A UNESCO-listed stepwell with intricate carvings dating back to the 11th century, showcasing the architectural and technological prowess of the Solanki dynasty.</p>
                    </div>
                </div>
                
                <!-- Medium item -->
                <div class="magazine-item magazine-medium" data-category="religious" data-id="somnath-temple-main">
                    <div class="magazine-img-container">
                        <img src="images/places/somnath-mandir.jpg" alt="Somnath Temple">
                    </div>
                    <div class="magazine-overlay">
                        <span class="magazine-category religious">Religious</span>
                        <h3 class="magazine-title">Somnath Temple</h3>
                        <p class="magazine-location">Somnath, Gujarat</p>
                        <p class="magazine-description">One of the 12 Jyotirlingas, dedicated to Lord Shiva, with a stunning seaside location.</p>
                    </div>
                </div>
                
                <!-- Small items -->
                <div class="magazine-item magazine-small" data-category="wildlife" data-id="gir-national-park-main">
                    <div class="magazine-img-container">
                        <img src="images/places/gir-national-park.jpg" alt="Gir National Park">
                    </div>
                    <div class="magazine-overlay">
                        <span class="magazine-category wildlife">Wildlife</span>
                        <h3 class="magazine-title">Gir National Park</h3>
                        <p class="magazine-location">Junagadh, Gujarat</p>
                    </div>
                </div>
                
                <div class="magazine-item magazine-small" data-category="modern" data-id="statue-of-unity-main">
                    <div class="magazine-img-container">
                        <img src="images/places/statue-of-unity.jpg" alt="Statue of Unity">
                    </div>
                    <div class="magazine-overlay">
                        <span class="magazine-category modern">Modern</span>
                        <h3 class="magazine-title">Statue of Unity</h3>
                        <p class="magazine-location">Kevadia, Gujarat</p>
                    </div>
                </div>
                
                <div class="magazine-item magazine-small" data-category="natural" data-id="white-rann-kutch-main">
                    <div class="magazine-img-container">
                        <img src="images/places/white-run-of-kutch.jpg" alt="White Rann of Kutch">
                    </div>
                    <div class="magazine-overlay">
                        <span class="magazine-category natural">Natural</span>
                        <h3 class="magazine-title">White Rann of Kutch</h3>
                        <p class="magazine-location">Kutch, Gujarat</p>
                    </div>
                </div>
                
                <div class="magazine-item magazine-medium" data-category="religious" data-id="dwarkadhish-temple-main">
                    <div class="magazine-img-container">
                        <img src="images/places/dwarkadhish-temple.jpg" alt="Dwarkadhish Temple">
                    </div>
                    <div class="magazine-overlay">
                        <span class="magazine-category religious">Religious</span>
                        <h3 class="magazine-title">Dwarkadhish Temple</h3>
                        <p class="magazine-location">Dwarka, Gujarat</p>
                        <p class="magazine-description">A famous Krishna temple with a history of over 2,000 years, one of the Char Dham pilgrimage sites.</p>
                    </div>
                </div>
            </div>
            
            <div class="magazine-title-section" style="margin-top: 80px;">
                <h2>Discover Ahmedabad</h2>
            </div>
            
            <div class="places-magazine-grid" id="ahmedabad-magazine">
                <!-- Feature item (large) -->
                <div class="magazine-item magazine-feature" data-category="historical" data-id="sabarmati-ashram-ahmd">
                    <div class="magazine-img-container">
                        <img src="images/places/sabarmati-ashram.jpg" alt="Sabarmati Ashram">
                    </div>
                    <div class="magazine-overlay">
                        <span class="magazine-category historical">Historical</span>
                        <h3 class="magazine-title">Sabarmati Ashram</h3>
                        <p class="magazine-location">Ahmedabad, Gujarat</p>
                        <p class="magazine-description">The residence of Mahatma Gandhi from 1917 to 1930 and a pivotal center for India's freedom struggle, offering a glimpse into the life and philosophy of the Father of the Nation.</p>
                    </div>
                </div>
                
                <!-- Medium items -->
                <div class="magazine-item magazine-medium" data-category="architecture" data-id="akshardham-ahmd">
                    <div class="magazine-img-container">
                        <img src="images/places/akshardham-mandir.jpg" alt="Akshardham Temple">
                    </div>
                    <div class="magazine-overlay">
                        <span class="magazine-category architecture">Architecture</span>
                        <h3 class="magazine-title">Akshardham Temple</h3>
                        <p class="magazine-location">Gandhinagar, near Ahmedabad</p>
                        <p class="magazine-description">A magnificent temple complex dedicated to Bhagwan Swaminarayan with stunning architecture and spiritual exhibitions.</p>
                    </div>
                </div>
                
                <!-- Small items (will be populated with appropriate images when available) -->
                <div class="magazine-item magazine-small" data-category="museums" data-id="calico-museum-ahmd">
                    <div class="magazine-img-container">
                        <img src="images/places/calico-museum.jpg" alt="Calico Museum">
                    </div>
                    <div class="magazine-overlay">
                        <span class="magazine-category museums">Museum</span>
                        <h3 class="magazine-title">Calico Museum of Textiles</h3>
                        <p class="magazine-location">Ahmedabad, Gujarat</p>
                    </div>
                </div>
                
                <div class="magazine-item magazine-small" data-category="historical" data-id="sidi-saiyyed-mosque">
                    <div class="magazine-img-container">
                        <img src="images/places/sidi-saiyyed-mosque.jpg" alt="Sidi Saiyyed Mosque">
                    </div>
                    <div class="magazine-overlay">
                        <span class="magazine-category historical">Historical</span>
                        <h3 class="magazine-title">Sidi Saiyyed Mosque</h3>
                        <p class="magazine-location">Ahmedabad, Gujarat</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Place Detail Modal -->
    <div class="place-detail-modal" id="placeDetailModal">
        <div class="modal-content">
            <!-- Modal content will be populated with JavaScript -->
        </div>
    </div>

    <!-- Main Footer -->
    <footer class="main-footer">
        <!--Widgets Section-->
        <div class="widgets-section">
            <div class="auto-container">
                <div class="row">
                    <!--Big Column-->
                    <div class="big-column col-xl-6 col-lg-12 col-md-12 col-sm-12">
                        <div class="row">
                            <!--Footer Column-->
                            <div class="footer-column col-xl-7 col-lg-6 col-md-6 col-sm-12">
                                <div class="footer-widget about-widget">
                                    <h2 class="widget-title">Secretariat</h2>
                                    <div class="logo">
                                        <a href="index.html"><img src="images/footer-logo.png" alt="" /></a>
                                    </div>
                                    <h4 class="clr-white">Dr. R. G. Patel</h4>
                                    <div class="text clr-white">Sunflower Infertility & IVF Center
                                        Drive In Rd, Near Helmet Cross-Roads,
                                        Memnagar, Ahmedabad - 380052.
                                        Gujarat, India</div>
                                </div>
                                <ul class="contact-list">
                                            
                                    <li>
                                        <span class="icon flaticon-phone"></span>
                                        <div class="text"><a href="tel:+************">+91 97272 42852</a></div>
                                    </li>
                                    <li>
                                        <span class="icon flaticon-phone"></span>
                                        <div class="text"><a href="tel:+************">+91 80006 22067</a></div>
                                    </li>

                                    <li>
                                        <span class="icon flaticon-paper-plane"></span>
                                        <div class="text">
                                            <a href="mailto:<EMAIL>"><EMAIL></a>
                                    </li>
                                </ul>
                            </div>

                            <!--Footer Column-->
                            <div class="footer-column col-xl-5 col-lg-6 col-md-6 col-sm-12">
                                <div class="footer-widget useful-links">
                                    <h2 class="widget-title">Useful Links</h2>
                                    <ul class="user-links">
                                        <li><a href="index.html">Home</a></li>
                                        <li><a href="committee.html">Committee</a></li>
                                        <li><a href="speakers.html">Speakers</a></li>
                                        <li><a href="registration.html">Registration</a></li>
                                        <li><a href="abstract.html">Abstract</a></li>
                                        <li><a href="schedule.html">Schedule</a></li>
                                        <li><a href="#">Travel</a></li>
                                        <li><a href="contact.html">Contact Us</a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!--Big Column-->
                    <div class="big-column col-xl-6 col-lg-12 col-md-12 col-sm-12">
                        <div class="row">
                            <!--Footer Column-->
                            <div class="footer-column col-lg-6 col-md-6 col-sm-12">
                                <!--Footer Column-->
                                <div class="footer-widget about-widget">
                                    <h2 class="widget-title">Professional Conference Organisers</h2>
                                    <div class="or-footer-logo">
                                        <a href="index.html"><img src="images/or-footer-logo.png" alt="" /></a>
                                    </div> <br>
                                     <!--Footer Column-->
                                     <div class="widget-content">
                                        <ul class="contact-list">
                                            
                                            <li>
                                                <span class="icon flaticon-phone"></span>
                                                <div class="text"><a href="tel:+************">+91 97272 42852</a></div>
                                            </li>
                                            <li>
                                                <span class="icon flaticon-phone"></span>
                                                <div class="text"><a href="tel:+************">+91 80006 22067</a></div>
                                            </li>
                                            <li>
                                                <span class="icon flaticon-paper-plane"></span>
                                                <div class="text"><a href="mailto:<EMAIL>"><EMAIL></a></div>
                                            </li>
                                            <li>
                                                <span class="icon flaticon-worldwide"></span>
                                                <div class="text"><a href="https://orangerose.in/" target="_blank">www.orangerose.in</a></div>
                                            </li>
                                        </ul>
                                    </div>
                                    <!-- <h4 class="clr-white">Orange Rose</h4> -->
                                    
                                </div> 
                            </div>

                            <!--Footer Column-->
                            <div class="footer-column col-lg-6 col-md-6 col-sm-12">
                                <!--Footer Column-->
                                <div class="footer-widget about-widget">
                                    <h2 class="widget-title">Official Accommodation Partner</h2>
                                    <div class="or-footer-logo">
                                        <a href="index.html"><img src="images/rx-footer-logo.png" alt="RX Logo" /></a>
                                    </div> 
                                    <br>
                                    <div class="widget-content">
                                        <h4 class="clr-white">Rx Events Pvt. Ltd.</h4>
                                        <div class="text clr-white">
                                            401, Shyamak Complex,<br>
                                            Opp. Sahjanand College, Ambavadi,<br> 
                                            Ahmedabad, Gujarat - 380015
                                        </div>
                                        <ul class="contact-list">
                                            <li>
                                                <span class="icon flaticon-phone"></span>
                                                <div class="text"><a href="tel:+919099331371">+91 9099331371/50</a></div>
                                            </li>
                                            <li>
                                                <span class="icon flaticon-paper-plane"></span>
                                                <div class="text"><a href="mailto:<EMAIL>"><EMAIL></a></div>
                                            </li>
                                            <li>
                                                <span class="icon flaticon-worldwide"></span>
                                                <div class="text"><a href="http://www.rxevents.co.in" target="_blank">www.rxevents.co.in</a></div>
                                            </li>
                                        </ul>
                                    </div>
                                    
                                </div> 
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--Footer Bottom-->
        <div class="footer-bottom">
            <div class="auto-container">
                <div class="inner-container clearfix">
                    <div class="powered-by-text pull-left">
                        <p class="clr-white">Powered by ISAR 2026</p>
                    </div>
                    <div class="copyright-text pull-right">
                        <p class="clr-white">Copyright ©Society for Assisted Reproduction | Managed by Orangerose Consulting LLP</p>
                    </div>
                </div>
                <p class="clr-white-footer"><a href="privacy-policy.html">Privacy Policy</a> | <a href="terms-condition.html">Terms & Conditions</a> | <a href="refund-policy.html">Refund Policy</a></p>
            </div>
        </div>
    </footer>
    <!-- End Footer -->

</div>
<!--End pagewrapper-->

<!-- Remove Color Palate / Color Switcher for production -->
<!-- <div class="color-palate">
    <div class="color-trigger">
        <i class="fa fa-cog"></i>
    </div>
    <div class="color-palate-head">
        <h6>Choose Your Demo</h6>
    </div>
    <ul class="box-version option-box"> <li>Full width</li> <li class="box">Boxed</li> </ul>
    <ul class="rtl-version option-box"> <li>LTR Version</li> <li class="rtl">RTL Version</li> </ul>
    <div class="palate-foo">
        <span>You will find much more options for colors and styling in admin panel. This color picker is used only for demonstation purposes.</span>
    </div>
    <a href="#" class="purchase-btn">Purchase now</a>
</div> -->
<!-- End Color Switcher -->

<!--Search Popup-->
<div id="search-popup" class="search-popup">
	<div class="close-search theme-btn"><span class="fas fa-window-close"></span></div>
	<div class="popup-inner">
		<div class="overlay-layer"></div>
    	<div class="search-form">
        	<form method="post" action="index.html">
            	<div class="form-group">
                	<fieldset>
                        <input type="search" class="form-control" name="search-input" value="" placeholder="Search Here" required >
                        <input type="submit" value="Search Now!" class="theme-btn">
                    </fieldset>
                </div>
            </form>

            <br>
            <h3>Recent Search Keywords</h3>
            <ul class="recent-searches">
                <li><a href="#">Seo</a></li>
                <li><a href="#">Bussiness</a></li>
                <li><a href="#">Events</a></li>
                <li><a href="#">Digital</a></li>
                <li><a href="#">Conferance</a></li>
            </ul>

        </div>

    </div>
</div>

<!--Scroll to top-->
<div class="scroll-to-top scroll-to-target" data-target="html"><span class="fa fa-angle-double-up"></span></div>
<script src="js/jquery.js"></script>
<script src="js/popper.min.js"></script>
<script src="js/bootstrap.min.js"></script>
<script src="js/jquery-ui.js"></script>
<script src="js/jquery.fancybox.js"></script>
<script src="js/appear.js"></script>
<script src="js/wow.js"></script>
<script src="js/swiper.min.js"></script>
<script src="js/places-data.js"></script>
<script src="js/places-to-visit.js"></script>
<script src="js/script.js"></script>
<!-- Color Setting -->
<script src="js/color-settings.js"></script>

<!-- Image optimization script -->
<script>
    // Function to lazy load background images
    document.addEventListener('DOMContentLoaded', function() {
        // Optimize page title background
        const pageTitle = document.querySelector('.page-title');
        if (pageTitle) {
            const bgImage = getComputedStyle(pageTitle).backgroundImage;
            if (bgImage && bgImage !== 'none') {
                const url = bgImage.slice(4, -1).replace(/["']/g, "");
                const img = new Image();
                img.onload = function() {
                    pageTitle.style.backgroundImage = `url(${url})`;
                };
                setTimeout(() => {
                    img.src = url;
                }, 100);
            }
        }
        
        // Optimize all images with lazy loading
        const images = document.querySelectorAll('img:not([loading])');
        images.forEach(img => {
            if (!img.hasAttribute('loading')) {
                img.setAttribute('loading', 'lazy');
            }
        });
    });
</script>
</body>
</html>







